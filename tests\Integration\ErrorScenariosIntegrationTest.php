<?php

declare(strict_types=1);

namespace Freemius\SDK\Tests\Integration;

use PHPUnit\Framework\TestCase;
use Freemius\SDK\FreemiusSDK;
use Freemius\SDK\Configuration;
use Freemius\SDK\Exceptions\AuthenticationException;
use Freemius\SDK\Exceptions\AuthorizationException;
use Freemius\SDK\Exceptions\NotFoundException;
use Freemius\SDK\Exceptions\ValidationException;
use Freemius\SDK\Exceptions\RateLimitException;
use Freemius\SDK\Exceptions\ServerException;
use Freemius\SDK\Exceptions\NetworkException;

/**
 * Integration tests for error scenarios and exception handling
 */
#[\PHPUnit\Framework\Attributes\CoversClass(\Freemius\SDK\Exceptions\FreemiusException::class)]
#[\PHPUnit\Framework\Attributes\Group('integration')]
class ErrorScenariosIntegrationTest extends TestCase
{
    private string $validBearerToken;

    protected function setUp(): void
    {
        // Skip integration tests if no bearer token is provided
        $this->validBearerToken = $_ENV['FREEMIUS_TEST_BEARER_TOKEN'] ?? '';
        if (empty($this->validBearerToken)) {
            $this->markTestSkipped('FREEMIUS_TEST_BEARER_TOKEN environment variable not set');
        }
    }

    public function testAuthenticationWithInvalidToken(): void
    {
        $config = new Configuration([
            'bearerToken' => 'invalid_token_12345',
            'sandbox' => true,
            'productScope' => 1,
            'timeout' => 10,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        $this->expectException(AuthenticationException::class);
        $this->expectExceptionMessage('Authentication failed');
        
        $sdk->products()->all();
    }

    public function testAuthenticationWithEmptyToken(): void
    {
        $config = new Configuration([
            'bearerToken' => '',
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        $this->expectException(AuthenticationException::class);
        
        $sdk->products()->all();
    }

    public function testAuthenticationWithMalformedToken(): void
    {
        $config = new Configuration([
            'bearerToken' => 'malformed-token-without-proper-format',
            'sandbox' => true,
            'productScope' => 1,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        $this->expectException(AuthenticationException::class);
        
        $sdk->products()->all();
    }

    public function testResourceNotFoundErrors(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // Test 404 errors for different resources
        $nonExistentIds = [999999, 888888, 777777];
        
        foreach ($nonExistentIds as $id) {
            try {
                $sdk->products()->get($id);
                $this->fail("Expected NotFoundException for product ID $id");
            } catch (NotFoundException $e) {
                $this->assertStringContainsString('not found', strtolower($e->getMessage()));
                $this->assertEquals(404, $e->getCode());
            }
            
            try {
                $sdk->users()->get($id);
                $this->fail("Expected NotFoundException for user ID $id");
            } catch (NotFoundException $e) {
                $this->assertStringContainsString('not found', strtolower($e->getMessage()));
            }
            
            try {
                $sdk->licenses()->get($id);
                $this->fail("Expected NotFoundException for license ID $id");
            } catch (NotFoundException $e) {
                $this->assertStringContainsString('not found', strtolower($e->getMessage()));
            }
        }
    }

    public function testValidationErrors(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // Test validation errors with invalid data
        try {
            $sdk->products()->create([
                'invalid_field' => 'invalid_value',
                'another_invalid_field' => 12345,
            ]);
            $this->fail('Expected ValidationException for invalid product data');
        } catch (ValidationException $e) {
            $this->assertStringContainsString('validation', strtolower($e->getMessage()));
            $this->assertEquals(400, $e->getCode());
        } catch (\Exception $e) {
            // Some endpoints might not support creation in sandbox, which is acceptable
            $this->assertInstanceOf(\Exception::class, $e);
        }

        // Test validation errors with invalid parameters
        try {
            $sdk->users()->all([
                'limit' => -1,  // Invalid limit
                'offset' => 'invalid_offset',  // Invalid offset type
            ]);
            $this->fail('Expected ValidationException for invalid parameters');
        } catch (ValidationException $e) {
            $this->assertStringContainsString('validation', strtolower($e->getMessage()));
        } catch (\Exception $e) {
            // API might handle this differently, which is acceptable for testing
            $this->assertInstanceOf(\Exception::class, $e);
        }
    }

    public function testRateLimitingHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
            'timeout' => 5,
            'retryAttempts' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // Make rapid requests to potentially trigger rate limiting
        $requestCount = 10;
        $rateLimitHit = false;
        
        for ($i = 0; $i < $requestCount; $i++) {
            try {
                $sdk->products()->all(['limit' => 1]);
                // Small delay to avoid overwhelming the API
                usleep(100000); // 0.1 seconds
            } catch (RateLimitException $e) {
                $rateLimitHit = true;
                $this->assertStringContainsString('rate limit', strtolower($e->getMessage()));
                $this->assertEquals(429, $e->getCode());
                
                // Check if retry-after header is available
                $retryAfter = $e->getRetryAfter();
                if ($retryAfter !== null) {
                    $this->assertIsInt($retryAfter);
                    $this->assertGreaterThan(0, $retryAfter);
                }
                break;
            } catch (\Exception $e) {
                // Other exceptions are acceptable
                continue;
            }
        }
        
        // Note: Rate limiting might not be triggered in sandbox, so we don't assert it must happen
        if ($rateLimitHit) {
            $this->assertTrue(true, 'Rate limiting was properly handled');
        } else {
            $this->markTestIncomplete('Rate limiting was not triggered during test');
        }
    }

    public function testNetworkTimeoutHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
            'timeout' => 1, // Very short timeout to trigger timeout errors
        ]);

        $sdk = new FreemiusSDK($config);

        try {
            $sdk->products()->all();
            // If this succeeds, the API was very fast, which is fine
            $this->assertTrue(true);
        } catch (NetworkException $e) {
            $this->assertStringContainsString('timeout', strtolower($e->getMessage()));
        } catch (\Exception $e) {
            // Other network-related exceptions are also acceptable
            $this->assertInstanceOf(\Exception::class, $e);
        }
    }

    public function testInvalidScopeHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // Test with invalid product scope
        try {
            $sdk->setProductScope(999999);
            $users = $sdk->users()->all();
            
            // If this succeeds, the API might not validate scope immediately
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
        } catch (NotFoundException $e) {
            $this->assertStringContainsString('not found', strtolower($e->getMessage()));
        } catch (AuthorizationException $e) {
            $this->assertStringContainsString('authorization', strtolower($e->getMessage()));
            $this->assertEquals(403, $e->getCode());
        } catch (\Exception $e) {
            // Other exceptions related to invalid scope are acceptable
            $this->assertInstanceOf(\Exception::class, $e);
        }
    }

    public function testMalformedResponseHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // This test is harder to trigger with real API, but we can test error handling
        try {
            // Make a request that might return unexpected data
            $result = $sdk->products()->all(['fields' => 'invalid_field_name']);
            
            // If successful, verify it's still a valid collection
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $result);
        } catch (\Exception $e) {
            // Any exception here is acceptable as we're testing error handling
            $this->assertInstanceOf(\Exception::class, $e);
        }
    }

    public function testServerErrorHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // Server errors are hard to trigger intentionally, but we can test the handling
        try {
            // Make requests that might trigger server errors
            $sdk->products()->all(['limit' => 999999]); // Potentially problematic limit
            
            // If successful, that's fine
            $this->assertTrue(true);
        } catch (ServerException $e) {
            $this->assertGreaterThanOrEqual(500, $e->getCode());
            $this->assertLessThan(600, $e->getCode());
        } catch (\Exception $e) {
            // Other exceptions are acceptable
            $this->assertInstanceOf(\Exception::class, $e);
        }
    }

    public function testExceptionContextAndDebugging(): void
    {
        $config = new Configuration([
            'bearerToken' => 'invalid_token_for_context_testing',
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        try {
            $sdk->products()->all();
            $this->fail('Expected exception for invalid token');
        } catch (AuthenticationException $e) {
            // Test that exception contains useful debugging information
            $this->assertIsString($e->getMessage());
            $this->assertNotEmpty($e->getMessage());
            
            // Test that we can get additional context
            $context = $e->getContext();
            if ($context !== null) {
                $this->assertIsArray($context);
            }
            
            // Test that exception has proper code
            $this->assertIsInt($e->getCode());
            $this->assertGreaterThan(0, $e->getCode());
        }
    }

    public function testConcurrentErrorHandling(): void
    {
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
            'timeout' => 10,
        ]);

        $sdk = new FreemiusSDK($config);

        // Test that errors are handled properly when making concurrent requests
        $errors = [];
        $successes = [];
        
        for ($i = 0; $i < 5; $i++) {
            try {
                if ($i % 2 === 0) {
                    // Valid request
                    $result = $sdk->products()->all(['limit' => 1]);
                    $successes[] = $result;
                } else {
                    // Invalid request
                    $result = $sdk->products()->get(999999);
                    $successes[] = $result; // Shouldn't reach here
                }
            } catch (\Exception $e) {
                $errors[] = $e;
            }
        }
        
        // Should have some errors and some successes
        $this->assertGreaterThan(0, count($errors));
        $this->assertGreaterThan(0, count($successes));
        
        // All errors should be proper exception instances
        foreach ($errors as $error) {
            $this->assertInstanceOf(\Exception::class, $error);
        }
        
        // All successes should be proper collections
        foreach ($successes as $success) {
            $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $success);
        }
    }

    public function testErrorRecoveryAfterFailure(): void
    {
        // Test that SDK can recover after encountering errors
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        // First, cause an error
        try {
            $sdk->products()->get(999999);
            $this->fail('Expected NotFoundException');
        } catch (NotFoundException $e) {
            $this->assertInstanceOf(NotFoundException::class, $e);
        }

        // Then, make a valid request to ensure SDK still works
        $products = $sdk->products()->all(['limit' => 1]);
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);

        // Cause another error
        try {
            $sdk->users()->get(888888);
            $this->fail('Expected NotFoundException');
        } catch (NotFoundException $e) {
            $this->assertInstanceOf(NotFoundException::class, $e);
        }

        // Make another valid request
        $users = $sdk->users()->all(['limit' => 1]);
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $users);
    }

    public function testCompleteErrorWorkflow(): void
    {
        // Test a complete error handling workflow
        $config = new Configuration([
            'bearerToken' => $this->validBearerToken,
            'sandbox' => true,
            'productScope' => 1,
        ]);

        $sdk = new FreemiusSDK($config);

        $errorTypes = [];
        
        // Test various error scenarios
        try {
            $sdk->products()->get(999999);
        } catch (NotFoundException $e) {
            $errorTypes[] = 'not_found';
            $this->assertEquals(404, $e->getCode());
        }

        try {
            $sdk->users()->create(['invalid' => 'data']);
        } catch (ValidationException $e) {
            $errorTypes[] = 'validation';
            $this->assertEquals(400, $e->getCode());
        } catch (\Exception $e) {
            // Creation might not be supported in sandbox
            $errorTypes[] = 'other';
        }

        // Verify we encountered expected error types
        $this->assertContains('not_found', $errorTypes);
        
        // After all errors, SDK should still work normally
        $products = $sdk->products()->all();
        $this->assertInstanceOf(\Freemius\SDK\Support\Collection::class, $products);
    }
}
